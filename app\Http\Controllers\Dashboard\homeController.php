<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Roles;
use App\Models\Businesses;
use App\Models\TourOperators;
use App\Models\Regions;
use App\Models\RegionalAgents;
use App\Models\Association;
use App\Models\Departures;
use App\Models\Payments;
use App\Models\Destinations;
use App\Models\DestinationAgents;
use App\Models\Taxes;

class homeController extends Controller
{

    public function loginView()
    {
        if (auth::check()) {
            return redirect()->route('dashboard.home'); // Redirect to the dashboard if logged in
        }
        return view('dashboard.auth.login');
    }
    public function signupView()
    {
        if (auth::check()) {
            return redirect()->route('dashboard.home'); // Redirect to the dashboard if logged in
        }
        return view('dashboard.auth.signup');
    }
    public function showResetForm($token)
    {
        $user = User::where('password_reset_token', $token)->first();

        if (!$user) {
            abort(404, 'Invalid or expired token.');
        }
        return view('dashboard.auth.resetPassword', compact('token'));
    }

    public function assetView()
    {
        return view('dashboard.dashboardAssets');
    }
    public function mediaView()
    {
        $user = Auth::user();
        $business = Businesses::first();
        // $images = json_decode($business->media, true);

        // Retrieve and decode `user_media`
        $userMedia = json_decode($business->user_media, true) ?? [];

        // Get the images for the logged-in user
        $images = $userMedia[$user->id] ?? [];

        return view('dashboard.media', compact('images'));
    }

  

    public function dashboardView()
    {
        $user = Auth::user();

        if ($user->role->role_key === 'association') {
            $operators = TourOperators::where('assoc_id', $user->association->id)->get();
            return view('dashboard.index', compact('operators'));
        }
        if ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin') {
            $regions = Regions::get();
            $associations = Association::get();
            $departures = Departures::get();
            $numDep = count($departures);
            $totalTourists = $departures->sum('total_tourist');

            return view('dashboard.index', compact('regions', 'associations', 'numDep', 'totalTourists'));
        }
        if ($user->role->role_key === 'tour_operator' && $user->tourOperator) {
            $departures = Departures::where('tour_operator_id', $user->tourOperator->id)->get();
            return view('dashboard.index', compact('departures'));
        }
        elseif ($user->role->role_key === 'tour_operator' && !$user->tourOperator) {
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();
            return redirect()->route('login')->with('error', 'Tour operator profile not found. Please contact administrator.');
        }
        return view('dashboard.index');
    }


    // business profile view
    public function profileView()
    {
        $business_detail = Businesses::first();

        $adminRole = Roles::where('role_key', 'admin')->first();

        $user_detail = User::where('role_id', $adminRole->id)->first();

        $business_users = User::where('role_id', '!=', $adminRole->id)->get();

        $role_detail = Roles::whereNotIn('role_key', ['admin,super_admin'])->get();

        return view('dashboard.businessProfile.index', compact('business_detail', 'user_detail', 'role_detail', 'business_users'));
    }

    public function businessUserUpdateView($id)
    {
        $businessUser = User::findOrFail($id);

        return view('dashboard.businessProfile.user.update', compact('businessUser'));
    }

    public function webSettingView()
    {
        $webSettings = Businesses::first();
        $logos = json_decode($webSettings->business_logo);

        // Handle contact emails safely
        $contact_email = [];
        if (!empty($webSettings->contact_email)) {
            $decodedEmail = json_decode($webSettings->contact_email, true);
            if (is_array($decodedEmail)) {
                $contact_email = implode(',', array_map(fn($email) => $email['value'], $decodedEmail));
            }
        } else {
            $contact_email = ''; // Set an empty string to avoid htmlspecialchars() error
        }

        // Handle newsletter emails safely
        $newsletter_email = [];
        if (!empty($webSettings->newsletter_email)) {
            $decodedNewsletterEmail = json_decode($webSettings->newsletter_email, true);
            if (is_array($decodedNewsletterEmail)) {
                $newsletter_email = implode(',', array_map(fn($email) => $email['value'], $decodedNewsletterEmail));
            }
        } else {
            $newsletter_email = ''; // Set an empty string
        }

        return view('dashboard.webSettings.index', compact('logos', 'contact_email', 'newsletter_email'));
    }


    // Association View routes
    public function associationView()
    {
        $association = Association::get();
        return view('dashboard.associations.index', compact('association'));
    }

    public function addAssociationView()
    {
        return view('dashboard.associations.add');
    }
    public function associationUpdateView($id)
    {
        $assoc = Association::findOrFail($id);
        return view('dashboard.associations.update', compact('assoc'));
    }


    // Association operators View routes
    public function assocOperatorsView()
    {
        $user = Auth::user();

        if ($user->role->role_key == 'admin' || $user->role->role_key == 'super_admin') {
            $operators = TourOperators::get();
        } else {
            $operators = TourOperators::where('assoc_id', $user->association->id)->get();
        }
        return view('dashboard.tourOperator.index', compact('operators'));
    }

    public function addAssocOperatorView()
    {
        return view('dashboard.tourOperator.add');
    }

    public function assocOperatorUpdateView($id)
    {
        $user = Auth::user();
        // Check if the operator belongs to the association
        $assocOperator = TourOperators::where('id', $id)
            ->where('assoc_id', $user->association->id)
            ->first();

        if (!$assocOperator) {
            return redirect()->route('tourOperators.list') // Change to your actual route name
                ->with('error', 'You are not authorized to access this operator.');
        }

        return view('dashboard.tourOperator.update', compact('assocOperator'));
    }

    // Reigons View routes
    public function regionsView()
    {
        $regions = Regions::get();
        return view('dashboard.regions.index', compact('regions'));
    }
    public function regionUpdateView($id)
    {
        $region = Regions::findOrFail($id);
        return view('dashboard.regions.update', compact('region'));
    }


    // Reigonal Agent View routes
    public function agentsView()
    {
        $agents = RegionalAgents::get();
        return view('dashboard.regionAgents.index', compact('agents'));
    }
    public function addAgentView()
    {
        $regions = Regions::get();
        return view('dashboard.regionAgents.add', compact('regions'));
    }

    public function agentUpdateView($id)
    {
        $agent = RegionalAgents::findOrFail($id);
        $regions = Regions::get();
        return view('dashboard.regionAgents.update', compact('agent', 'regions'));
    }


    // Destinations View routes
    public function destinationsView()
    {
        $user = Auth::user();
        $regionAgent = $user->agentRegion;
        $taxes = Taxes::get();
        if ($user->role->role_key == 'admin' || $user->role->role_key == 'super_admin') {
            $destinations = Destinations::get();
        } else {
            $destinations = Destinations::where('region_id', $regionAgent->region_id)->get();
        }

        return view('dashboard.destinations.index', compact('destinations','taxes'));
    }

    public function destinationUpdateView($id)
    {
        $user = Auth::user();
        $regionAgent = $user->agentRegion;
        $taxes = Taxes::get();

        // Check if the destination belongs to the user's region
        $destination = Destinations::where('id', $id)
            ->where('region_id', $regionAgent->region_id)
            ->first();

        if (!$destination) {
            return redirect()->route('destinations.list') // Change to your actual route name
                ->with('error', 'You are not authorized to access this destination.');
        }
        // Check if the destination is approved (status = 1)
        if ($destination->status == 1) {
            return redirect()->route('destinations.list')
                ->with('error', 'This destination is approved and cannot be updated.');
        }

        return view('dashboard.destinations.update', compact('destination','taxes'));
    }

    // Destination Agent View routes
    public function destinationsAgentsView()
    {
        $user = Auth::user();

        if ($user->role->role_key == 'admin' || $user->role->role_key == 'super_admin') {
            $agents = DestinationAgents::get();
        } else {
            $destinationIds = Destinations::where('region_id', $user->agentRegion->region_id)->pluck('id');
            // Fetch destination agents for these destinations
            $agents = DestinationAgents::whereIn('destination_id', $destinationIds)->get();
        }

        return view('dashboard.destinationAgents.index', compact('agents'));
    }

    public function addDestinationAgentView()
    {
        $user = Auth::user();
        $destinations = Destinations::where('region_id', $user->agentRegion->region_id)->get();

        return view('dashboard.destinationAgents.add', compact('destinations'));
    }

    public function destinationsAgentUpdateView($id)
    {
        $user = Auth::user();
        $agent = DestinationAgents::findOrFail($id);
        // Check if the agent belongs to a destination in the same region
        if ($agent->destination->region_id !== $user->agentRegion->region_id) {
            return redirect()->route('destinations.agent.list') // Change to your actual route name
                ->with('error', 'You are not authorized to access this destination.');
        }
        return view('dashboard.destinationAgents.update', compact('agent'));
    }



    // operator Departures View routes
    public function operatorDeparturesView()
    {
        $user = Auth::user();

        if ($user->role->role_key == 'admin' || $user->role->role_key == 'super_admin') {
            $departure = Departures::get();
        } else if ($user->role->role_key == 'association') {
            // Get all tour operators under the logged-in association
            $operatorIds = TourOperators::where('assoc_id', $user->association->id)->pluck('id');
            // Get departures for these operators
            $departure = Departures::whereIn('tour_operator_id', $operatorIds)->get();
        } else {
            $departure = Departures::where('tour_operator_id', $user->tourOperator->id)->get();
        }

        // Decode destinations column for each departure and collect unique IDs
        $destinationIds = $departure->map(function ($departure) {
            return json_decode($departure->destinations, true) ?? [];
        })->flatten()->unique()->toArray();

        // Fetch region names indexed by ID and convert to an array
        $destinations = Destinations::whereIn('id', $destinationIds)->pluck('name', 'id')->toArray();

        return view('dashboard.operatorDepartures.index', compact('departure', 'destinations'));
    }

    public function addDepartureView()
    {
        // Get all destinations where status is 1
        $destination = Destinations::where('status', 1)->get();
        // Get all regions
        $regions = Regions::all();

        // Initialize an array to store destinations by region name
        $destinationsByRegionName = [];

        // Loop through each region and get destinations related to it
        foreach ($regions as $region) {
            // Use the region's name as the key and assign its destinations as the value
            $destinationsByRegionName[$region->region] = $region->destinations()->where('status', 1)->get();
        }

        return view('dashboard.operatorDepartures.add', compact('destination', 'destinationsByRegionName'));
    }

    public function operatorDepartureUpdateView($id)
    {
        return redirect()->route('departures.list') // Change to your actual route name
            ->with('error', 'Departure cannot be changed after create');

        $user = Auth::user();

        $destinations = Destinations::get();

        // Check if the departure belongs to the operator
        $departure = Departures::where('id', $id)
            ->where('tour_operator_id', $user->tourOperator->id)
            ->first();

        if (!$departure) {
            return redirect()->route('departures.list') // Change to your actual route name
                ->with('error', 'You are not authorized to access this departure.');
        }

        // Get all regions
        $regions = Regions::all();

        // Initialize an array to store destinations by region name
        $destinationsByRegionName = [];

        // Loop through each region and get destinations related to it
        foreach ($regions as $region) {
            // Use the region's name as the key and assign its destinations as the value
            $destinationsByRegionName[$region->region] = $region->destinations;
        }

        return view('dashboard.operatorDepartures.update', compact('departure', 'destinations', 'destinationsByRegionName'));
    }

    // operator Departures payment View routes
    public function destinationPaymentsView()
    {
        $user = Auth::user();

        if ($user->role->role_key == 'admin' || $user->role->role_key == 'super_admin') {

            $payments = Payments::get();
        } else if ($user->role->role_key == 'association') {
            // Get all tour operators under the logged-in association
            $operatorIds = TourOperators::where('assoc_id', $user->association->id)->pluck('id');

            // Get departures for these operators
            $departureIds = Departures::whereIn('tour_operator_id', $operatorIds)->pluck('id');

            $payments = Payments::whereIn('departure_id', $departureIds)->get();
        } else if ($user->role->role_key == 'tour_operator') {

            // Get departures for the login operators
            $departureIds = Departures::where('tour_operator_id', $user->tourOperator->id)->pluck('id');

            $payments = Payments::whereIn('departure_id', $departureIds)->get();
        } else {
            $destinationAgent = $user->destinationAgent;
            // Fetch payments with related departure and tour operator
            $payments = Payments::where('destination_id', $destinationAgent->destination_id)
                ->with(['departure.tourOperator'])
                ->get();
        }

        return view('dashboard.payments.index', compact('payments'));
    }

    public function addDestinationPaymentView()
    {
        $user = Auth::user();
        $destinationAgent = $user->destinationAgent;
        $destination = $destinationAgent->destination;
        $assoc = Association::get();
        $serialNumber = Payments::generateSerialNumber();
        return view('dashboard.payments.add', compact('assoc', 'destination','serialNumber'));
    }

    public function updateDestinationPaymentView($id)
    {

        $payment = Payments::findOrFail($id);
        $user = Auth::user();

        $departure = $payment->departure;
        $tourOperator = $payment->departure->tourOperator;
        $association = $payment->departure->tourOperator->association;

        if ($user->role->role_key == 'destination_agent') {
            $destinationAgent = $user->destinationAgent;
            $destination = $destinationAgent->destination;
        } else if ($user->role->role_key == 'tour_operator') {

            // Decode JSON stored in destinations column
            $destinationIds = json_decode($departure->destinations, true);

            // Find the specific destination based on $payment->destination_id
            $destination = Destinations::whereIn('id', $destinationIds)
                ->where('id', $payment->destination_id)
                ->first();
        }



        return view('dashboard.payments.update', compact('payment', 'destination', 'departure', 'tourOperator', 'association'));
    }
}
