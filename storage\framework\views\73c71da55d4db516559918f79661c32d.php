<?php
$user = auth()->user();
?>
<div class="header_parent d-inline-block w-100">
    <div class="content_container">
        <div class="header d-flex align-items-center justify-content-between w-100">
            <div class="control_btn d-flex align-items-center gap-3">
                <i class="sidebar_toggle_btn fa-solid fa-bars"></i>

            </div>
            <div class="d-flex align-items-center gap-4">
                <div class="profile_filter d-flex align-items-center">
                    <label type="button" id="sort_toggle" data-bs-toggle="dropdown" aria-expanded="false" class="">
                        <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>"> <?php echo e($user->name); ?> <i
                            class="fas fa-chevron-down"></i></label>
                    <div class="filter_dropdown_main dropdown-menu" aria-labelledby="sort_toggle">
                        <ul class="filter_dropdown d-grid w-100">



                            <?php if($user && $user->role && $user->role->role_key === 'super_admin'): ?>


                            <li class="w-100"><a href="<?php echo e(route('business.profile')); ?>" class="w-100 d-flex align-items-center"><i class="far fa-user"></i> Account
                                </a></li>

                            <?php endif; ?>

                            <li class="w-100"><a id="logout_btn" class="w-100 d-flex align-items-center"><i class="fas fa-sign-out-alt"></i>
                                    Logout</a></li>
                        </ul>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div><?php /**PATH D:\clients project\yared\Tour Laravel Portal\yaredTourPortal\resources\views/dashboard/include/header.blade.php ENDPATH**/ ?>