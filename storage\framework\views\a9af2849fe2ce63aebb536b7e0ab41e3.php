<div class="dashboard_sidebar d-flex flex-column position-fixed overflow-y-auto">
    <div class="sidebar_control_btn control_btn justify-content-end mb-4 mr-4">
        <i class="sidebar_toggle_btn fa-solid fa-bars"></i>
    </div>
    <div class="sidebar_logo d-flex w-100 align-items-center justify-content-center">
        <img src="/images/logo.png">
    </div>



    <div class="filters_accordion_parent d-grid w-100">

        <ul class="filters_listing d-flex flex-column w-100 align-items-start overflow-y-auto">


            <?php
            $user = auth()->user();
            ?>


            <li class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/home') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('dashboard/home')); ?>" class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Dashboard</a>
            </li>
           

            <?php if($user && $user->role && ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>
            <li
                class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/regions') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('/dashboard/regions')); ?>"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Regions</a>
            </li>
            <li
                class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/region/agent') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('/dashboard/region/agent')); ?>"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Tourism bureau</a>
            </li>
            <?php endif; ?>

            <?php if($user && $user->role && ($user->role->role_key === 'regional_agent' || $user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>
            <li
                class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/destinations') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('/dashboard/destinations')); ?>"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Regional Destinations</a>
            </li>
            <li
                class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/destination/agent') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('/dashboard/destination/agent')); ?>"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Destination Agents</a>
            </li>
            <?php endif; ?>

            <?php if($user && $user->role && ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>
            <li
                class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/associations') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('/dashboard/associations')); ?>"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Stakeholders</a>
            </li>
            <?php endif; ?>

            <?php if($user && $user->role && ($user->role->role_key === 'association' || $user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>
            <li
                class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/association/operator') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('/dashboard/association/operator')); ?>"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Tour Operator Lsting</a>
            </li>
            <?php endif; ?>
            <?php if($user && $user->role && ($user->role->role_key === 'tour_operator' || $user->role->role_key === 'association' || $user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>
           
            <li
                class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/operator/departures') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('/dashboard/operator/departures')); ?>"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Manage Departure</a>
            </li>
           
            <?php endif; ?>
            <?php if($user && $user->role && ($user->role->role_key === 'tour_operator' || $user->role->role_key === 'association' || $user->role->role_key === 'admin' || $user->role->role_key === 'super_admin' || $user->role->role_key === 'destination_agent')): ?>
           
           <li
               class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/destination/payments') ? 'active' : ''); ?>">
               <a href="<?php echo e(url('/dashboard/destination/payments')); ?>"
                   class="d-flex align-items-center w-100 position-relative"><span
                       class="bullet position-relative"></span>Payments</a>
           </li>
           <?php endif; ?>

           <?php if($user && $user->role && ($user->role->role_key != 'destination_agent')): ?>
            <li class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/media') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('dashboard/media')); ?>" class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Media</a>
            </li>
            <?php endif; ?>
        </ul>




    </div>


</div><?php /**PATH D:\clients project\yared\Tour Laravel Portal\yaredTourPortal\resources\views/dashboard/include/sidebar.blade.php ENDPATH**/ ?>