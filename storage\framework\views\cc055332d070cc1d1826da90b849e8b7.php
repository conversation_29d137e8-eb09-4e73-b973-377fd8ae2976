
<!-- Modal -->
<div  class="modal fade" id="galleryModal" tabindex="-1" aria-labelledby="galleryModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="galleryModalLabel">Gallery</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
      <div class="indication d-grid w-100 gap-2 mb-3" >
        <span>Compress Image before upload if image greater then 1MB with any Below links !</span>
        <small class="d-grid w-100 gap-2">
            <a href="https://tinypng.com/" target="blank">https://tinypng.com/</a>
            <a href="https://www.compress2go.com/compress-image">https://www.compress2go.com/compress-image</a>
        </small>
    </div>
    <div class="url_link d-grid w-100 gap-2" id="url_link">
        <span>Uploaded Link !</span>
        <small></small>
    </div>
    <div class="data_box row">
      
        <div class="single_box col-12 col-md-12 col-lg-12">
            <form method="POST" id="upload_web_image" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="form_field parent_field position-relative col-12 col-md-12 col-lg-12">
                    <span>Website images</span>
                    <input id="web_images" type="file" name="web_images" class="dropify" data-max-file-size="2M" data-allowed-file-extensions="jpg jpeg png webp doc docx pdf" />
                    <label for="web_images" generated="true" class="error"></label>
                </div>

                <div class="form_button d-flex w-100 justify-content-end">
                      <button type="submit" class="w-100 d-flex align-items-center justify-content-center position-relative">Upload  <?php echo $__env->make('dashboard/include/loader', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                         </button>
                        </div>
            </form>
        </div>
    </div>
      <div class="file_gallery d-grid w-100 gallery">

     </div>

      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div><?php /**PATH D:\clients project\yared\Tour Laravel Portal\yaredTourPortal\resources\views/dashboard/mediaPopup.blade.php ENDPATH**/ ?>