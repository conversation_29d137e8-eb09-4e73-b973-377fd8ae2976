<?php $__env->startSection('title', 'home'); ?>
<?php $__env->startSection('content'); ?>

<?php
$user = auth()->user();
?>

<?php if($user && $user->role && ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>

<div class="data_box row">
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Depatures</h5>
                <span><?php echo e($numDep); ?></span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Tourist</h5>
                <span><?php echo e($totalTourists); ?></span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>
  
</div>

<div class="heading_box_two d-flex w-100">
    <h3>Tourist Graphs</h3>
</div>

<div class="graphs row">
<div class="single_graph col-12 col-md-12 col-lg-12">
        <canvas id="touristsChart" class="w-100 h-100"></canvas>
    </div>

 
    <div class="single_graph col-12 col-md-12 col-lg-12">
        <canvas id="destinationTouristCountData" class="w-100 h-100"></canvas>
    </div>


</div>

<hr />

<div class="graphs row">
<div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="nationalityVStourist" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="assocOperatorData" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="regionDestinationData" class="w-100 h-100"></canvas>
    </div>

</div>

<div class="heading_box_two d-flex w-100">
    <h3>Expense Graphs</h3>
</div>

<form method="POST" id="admin_filter_data" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>

    <div class="form_field_group row">
        <!-- <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Region</span>
            <select name="region" id="regionFilter">
                <option selected disabled>select region</option>
                <?php $__currentLoopData = $regions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $region): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($region->id); ?>"><?php echo e($region->region); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
            <label for="region" generated="true" class="error"></label>
        </div>
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Destination</span>
            <select name="destination" id="destinationFilter">
                <option selected disabled>select destination</option>

            </select>
            <label for="destination" generated="true" class="error"></label>
        </div> -->

        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Stakeholder</span>
            <select name="assoc" id="assocFilter">
                <option selected disabled>select Stakeholder</option>
                <?php $__currentLoopData = $associations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $association): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($association->id); ?>"><?php echo e($association->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
            <label for="assoc" generated="true" class="error"></label>
        </div>
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Tour Operator</span>
            <select name="tourOperator" id="tourOperatorFilter">
                <option selected disabled>select operator</option>
            </select>
            <label for="tourOperator" generated="true" class="error"></label>
        </div>
        <div class="operator_data child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Departure</span>
            <select name="departure" id="tourOperatorDepartureFilter">
                <option selected disabled>select departure</option>
            </select>

            <label for="departure" generated="true" class="error"></label>
        </div>
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <div class="form_button d-flex w-100 justify-content-end"><button type="submit">Filter</button></div>
        </div>


    </div>


</form>


<div class="graphs row">

    <div class="single_graph col-12 col-md-6 col-lg-6" id="adminFilterExpenceGraph" style="display:none;">
        <canvas id="filterExpenseData" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="regionExpenceData" class="w-100 h-100"></canvas>
    </div>

    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="destinationExpenseData" class="w-100 h-100"></canvas>
    </div>

    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="assocExpenseData" class="w-100 h-100"></canvas>
    </div>
</div>

<?php endif; ?>

<?php if($user && $user->role && ($user->role->role_key === 'regional_agent')): ?>
<div class="graphs row">
<div class="single_graph col-12 col-md-12 col-lg-12">
        <canvas id="touristsChart" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="regionalAgentChart" class="w-100 h-100"></canvas>
    </div>
</div>
<?php endif; ?>

<?php if($user && $user->role && ($user->role->role_key === 'destination_agent')): ?>
<div class="graphs row">
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="destinationAgentChart" class="w-100 h-100"></canvas>
    </div>
</div>
<?php endif; ?>

<?php if($user && $user->role && $user->role->role_key === 'association'): ?>

<div class="heading_box_two d-flex w-100">
    <h3>Expense Graphs</h3>
</div>

<form method="POST" id="assoc_filter_data" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>

    <div class="form_field_group row">

        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Tour Operator</span>
            <select name="tourOperator" id="tourOperatorFilter">
            <option selected disabled>select operator</option>
                <?php $__currentLoopData = $operators; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $operator): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($operator->id); ?>"><?php echo e($operator->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>

            <label for="tourOperator" generated="true" class="error"></label>
        </div>

        <div class="operator_data child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Departure</span>
            <select name="departure" id="tourOperatorDepartureFilter">
                <option selected disabled>select departure</option>
            </select>

            <label for="departure" generated="true" class="error"></label>
        </div>
     
    </div>
    <div class="form_button d-flex w-100 justify-content-end"><button type="submit">Filter</button></div>


</form>

<div class="graphs row">
    <div class="single_graph col-12 col-md-6 col-lg-6" id="assocFilterExpenceGraph" style="display:none;">
        <canvas id="assocFilterExpenseData" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="assocExpenseData" class="w-100 h-100"></canvas>
    </div>
    <!-- Bar Chart for Tour Operators & Their Departures -->
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="associationTourOperatorsChart" class="w-100 h-100"></canvas>
    </div>


</div>
<?php endif; ?>
<?php if($user && $user->role && ($user->role->role_key === 'tour_operator')): ?>



<div class="graphs row">
<div class="single_graph col-12 col-md-12 col-lg-12">
        <canvas id="touristsChart" class="w-100 h-100"></canvas>
    </div>
    </div>
    <div class="heading_box_two d-flex w-100">
    <h3>Expense Graphs</h3>
</div>
<form method="POST" id="tourOperator_filter_data" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>

    <div class="form_field_group row">

        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Departure</span>
            <select name="departure">
                <option selected disabled>select departure</option>
                <?php $__currentLoopData = $departures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $departure): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($departure->id); ?>"><?php echo e($departure->date_range); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>

            <label for="departure" generated="true" class="error"></label>
        </div>

        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
           
        <div class="form_button d-flex w-100 justify-content-start"><button type="submit">Filter</button></div>

        </div>
     
    </div>
   

</form>


<div class="graphs row">
<div class="single_graph col-12 col-md-6 col-lg-6" id="tourOperatorFilterExpenceGraph" style="display:none;">
        <canvas id="tourOperatorFilterExpenseData" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="tourOperatorChart" class="w-100 h-100"></canvas>
    </div>
</div>
<?php endif; ?>

</div>


<?php $__env->stopSection(); ?>
<?php echo $__env->make('dashboard.include.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\yared\Tour Laravel Portal\yaredTourPortal\resources\views/dashboard/index.blade.php ENDPATH**/ ?>